import { jwtVerify, SignJWT } from 'jose';
import { ExternalUser } from '../types/iframe';

// Secret key for JWT signing and verification
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'default-jwt-secret-for-iframe-authentication'
);

// JWT token expiration time (24 hours by default)
const TOKEN_EXPIRATION = '24h';

/**
 * Validates an iframe token and returns whether it's valid
 * @param token The JWT token to validate
 * @returns boolean indicating if the token is valid
 */
export async function validateIframeToken(token: string): Promise<boolean> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return !!payload;
  } catch (error) {
    console.error('Error validating iframe token:', error);
    return false;
  }
}

/**
 * Extracts user data from a JWT token
 * @param token The JWT token containing user data
 * @returns The external user data or null if invalid
 */
export function extractUserDataFromToken(token: string | undefined): ExternalUser | null {
  if (!token) {
    console.error("JWT token is undefined or empty")
    return null
  }
  try {
    // For synchronous extraction, we use a simpler approach
    // This assumes the token is already validated elsewhere if needed
    const payload = JSON.parse(
      Buffer.from(token.split('.')[1], 'base64').toString()
    )
    
    if (!payload.externalId || !payload.source) {
      console.error('Invalid token payload - missing required fields')
      return null
    }
    
    return {
      externalId: payload.externalId,
      source: payload.source,
      metadata: payload.metadata || {}
    }
  } catch (error) {
    console.error('Error extracting user data from token:', error);
    return null;
  }
}

/**
 * Creates a JWT token for an external user
 * @param userData The external user data to encode in the token
 * @returns The generated JWT token
 */
export async function createIframeToken(userData: ExternalUser): Promise<string> {
  try {
    const token = await new SignJWT({
      externalId: userData.externalId,
      source: userData.source,
      metadata: userData.metadata || {}
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(TOKEN_EXPIRATION)
      .sign(JWT_SECRET);
    
    return token;
  } catch (error) {
    console.error('Error creating iframe token:', error);
    throw new Error('Failed to create authentication token');
  }
}