'use client';

import { useMemo, useOptimistic, useState } from 'react';
import type { Button } from '@/components/ui/button';
import { chatModels } from '@/lib/ai/models';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import type { Session } from 'next-auth';

export function ModelSelector({
  session,
  selectedModelId,
  className,
}: {
  session: Session | null;
  selectedModelId: string;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false)
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId)

  // Đ<PERSON><PERSON> bảo hook luôn đ<PERSON><PERSON><PERSON> gọi đúng thứ tự
  const userType = session?.user?.type
  let entitlements = userType ? entitlementsByUserType[userType] : undefined

  if (!entitlements && userType) {
    console.warn(
      `No entitlements found for user type: ${userType}, falling back to guest`,
    )
    entitlements = entitlementsByUserType.guest
  }

  const availableChatModelIds = entitlements?.availableChatModelIds ?? []

  const availableChatModels = useMemo(
    () =>
      chatModels.filter((chatModel) =>
        availableChatModelIds.includes(chatModel.id),
      ),
    [availableChatModelIds],
  )

  const selectedChatModel = useMemo(
    () =>
      availableChatModels.find(
        (chatModel) => chatModel.id === optimisticModelId,
      ),
    [optimisticModelId, availableChatModels],
  )

  // Handle case where session might be null hoặc không có user type
  if (!userType) {
    return null
  }

  // Nếu chỉ có 1 model, không hiển thị gì
  if (availableChatModels.length <= 1) {
    return null
  }

  // TODO: Implement full model selector UI
  return null
}
