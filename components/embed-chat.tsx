'use client';

import type { Attachment, UIMessage } from "ai"
import { useChat } from "@ai-sdk/react"
import { useEffect, useState, Suspense } from "react"
import useSWR, { useSWRConfig } from "swr"
import { unstable_serialize } from "swr/infinite"
import type { Session } from "next-auth"
import { useSearchParams } from "next/navigation"

import { EmbedChatHeader } from "@/components/embed-chat-header"
import { useChatVisibility } from "@/hooks/use-chat-visibility"
import { useAutoResume } from "@/hooks/use-auto-resume"
import { ChatSDKError } from "@/lib/errors"
import type { Vote } from "@/lib/db/schema"
import { fetcher, fetchWithErrorHandlers, generateUUID } from "@/lib/utils"

import { MultimodalInput } from "./multimodal-input"
import { Messages } from "./messages"
import type { VisibilityType } from "./visibility-selector"
import { toast } from "./toast"

function buildApiUrl(session: Session): string {
  const params = new URLSearchParams()

  if (session.user.externalId) params.append("user_id", session.user.externalId)
  if (session.user.source) params.append("source", session.user.source)
  if (session.user.token) params.append("token", encodeURIComponent(session.user.token))
  if (session.user.tenant) params.append("tenant", encodeURIComponent(session.user.tenant))
  if (session.user.userName) params.append("user_name", encodeURIComponent(session.user.userName))

  return `/api/embed/chat?${params.toString()}`
}

function EmbedChatInner({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
}) {
  const { mutate } = useSWRConfig();

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
    data,
  } = useChat({
    id,
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    fetch: fetchWithErrorHandlers,
    api: buildApiUrl(session), // Use embed-specific API with all parameters
    experimental_prepareRequestBody: (body) => ({
      id,
      message: body.messages.at(-1),
      selectedChatModel: initialChatModel,
      selectedVisibilityType: visibilityType,
    }),
    onFinish: () => {
      // Skip history mutation for embed mode
    },
    onError: (error) => {
      if (error instanceof ChatSDKError) {
        toast({
          type: 'error',
          description: error.message,
        });
      }
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  const searchParams = useSearchParams();
  const selectedModelId = searchParams?.get('model') ?? initialChatModel;
  const query = searchParams?.get('query');

  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: 'user',
        content: query,
      });

      setHasAppendedQuery(true);
      window.history.replaceState({}, '', `/embed?${new URLSearchParams(window.location.search).toString().replace(/[?&]query=[^&]*/, '')}`);
    }
  }, [query, append, hasAppendedQuery]);

  // Tạm thời disable auto-resume để tránh lỗi
  // useAutoResume({
  //   autoResume,
  //   initialMessages,
  //   experimental_resume,
  //   data,
  //   setMessages,
  // });

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        stop();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [stop]);

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background w-full">
      <EmbedChatHeader
        chatId={id}
        selectedVisibilityType={visibilityType}
        isReadonly={isReadonly}
      />

      <Messages
        chatId={id}
        status={status}
        votes={votes}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        isReadonly={isReadonly}
      />

      {!isReadonly && (
        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          <MultimodalInput
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            status={status}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
            selectedVisibilityType={visibilityType}
          />
        </form>
      )}
    </div>
  );
}

export function EmbedChat(props: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
}) {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
      <EmbedChatInner {...props} />
    </Suspense>
  );
}
