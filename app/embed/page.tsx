import { redirect } from 'next/navigation';
import { generateUUID } from '@/lib/utils';
import { createOrGetExternalUser } from '@/lib/db/queries';
import { EmbedChatWrapper } from '@/components/embed-chat-wrapper';

interface EmbedPageProps {
  searchParams: Promise<{
    token?: string;
    user_id?: string;
    source?: string;
    tenant?: string;
    user_name?: string;
  }>;
}

async function createEmbedSession(token?: string, userId?: string, source?: string, tenant?: string, userName?: string) {
  console.log('🔐 Creating embed session with:', {
    hasToken: !!token,
    userId,
    source,
    tenant,
    userName,
    tokenLength: token?.length
  });

  try {
    // Simple user_id + source authentication (primary method)
    if (userId && source) {
      console.log('🔑 Using simple authentication with user_id + source...');
      const userData = {
        externalId: userId,
        source: source as any,
        metadata: {
          token, // Store token as metadata for reference
          tenant,
          userName,
          lastAccess: new Date().toISOString(),
        },
      };

      const user = await createOrGetExternalUser(userData);
      console.log('✅ Simple auth successful, user:', { id: user.id, email: user.email });

      return {
        user: {
          id: user.id,
          email: user.email,
          type: 'external' as const,
          externalId: userId,
          source: source,
          token,
          tenant,
          userName,
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
    }

    console.error('❌ No valid authentication provided - missing user_id or source');
    throw new Error('No valid authentication provided');
  } catch (error) {
    console.error('💥 Failed to create embed session:', error);
    throw error;
  }
}

export default async function EmbedPage({ searchParams }: EmbedPageProps) {
  console.log('🚀 EmbedPage loading with searchParams:', searchParams);

  // Await searchParams to comply with Next.js 15 async API
  const params = await searchParams;
  const { token, user_id, source, tenant, user_name } = params;

  // Redirect to auth error if no authentication provided
  if (!token && !user_id) {
    console.log('❌ No authentication provided, redirecting to auth error');
    redirect('/embed/auth-error?error=missing_auth');
  }

  try {
    const session = await createEmbedSession(token, user_id, source, tenant, user_name);
    const id = generateUUID();

    console.log('🎉 Embed page loaded successfully with external session:', {
      externalId: session.user.externalId,
      source: session.user.source,
      tenant: session.user.tenant,
      userName: session.user.userName,
      chatId: id
    });

    return (
      <EmbedChatWrapper
        id={id}
        initialMessages={[]}
        initialChatModel="chat-model"
        initialVisibilityType="private"
        isReadonly={false}
        session={session}
        autoResume={false}
      />
    );
  } catch (error) {
    console.error('💥 Embed authentication failed:', error);
    const errorType = error instanceof Error && error.message.includes('Invalid token')
      ? 'invalid_token'
      : 'auth_failed';
    redirect(`/embed/auth-error?error=${errorType}`);
  }
}
