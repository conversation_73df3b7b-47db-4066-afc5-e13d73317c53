import {
  appendClientMessage,
  appendResponseMessages,
  createDataStream,
  smoothStream,
  streamText,
} from 'ai';
import { type RequestHints, enhancedSystemPrompt } from '@/lib/ai/prompts';
import {
  createStreamId,
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  saveChat,
  saveMessages,
  createOrGetExternalUser,
} from '@/lib/db/queries';
import { generateUUID, getTrailingMessageId } from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../../(chat)/actions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { getPathRAGKnowledge } from '@/lib/ai/tools/pathrag-knowledge';
import { createTicket } from '@/lib/ai/tools/create-ticket';

import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { validateUsage } from '@/lib/ai/entitlements';
import { postRequestBodySchema, type PostRequestBody } from '../../../(chat)/api/chat/schema';
import { geolocation } from '@vercel/functions';
import { ChatSDKError } from '@/lib/errors';

export const maxDuration = 60;

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (error) {
    console.error('API Embed Chat parsing failed:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    const { id, message, selectedChatModel, selectedVisibilityType } = requestBody;

    // Get user_id and source from headers or query params for embed authentication
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id') || request.headers.get('x-user-id');
    const source = searchParams.get('source') || request.headers.get('x-source');
    const token = searchParams.get('token') || request.headers.get('x-token');
    const tenant = searchParams.get('tenant') || request.headers.get('x-tenant');
    const userName = searchParams.get('user_name') || request.headers.get('x-user-name');

    if (!userId || !source) {
      console.error('Embed authentication failed: missing user_id or source');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    console.log('🚀 Embed chat request:', { 
      userId, 
      source, 
      chatId: id,
      token: token ? '***' : undefined, // Hide token in logs
      tenant,
      userName
    });

    // Find the external user by their external ID and source
    // Use createOrGetExternalUser to find or create the user
    const userData = {
      externalId: userId,
      source: source as any,
      metadata: {
        token,
        tenant,
        userName,
        lastAccess: new Date().toISOString(),
      },
    };

    const user = await createOrGetExternalUser(userData);
    console.log('✅ Found/created external user:', { id: user.id, email: user.email });

    const messageCountDay = await getMessageCountByUserId({
      id: user.id,
      differenceInHours: 24,
    });

    const messageCountHour = await getMessageCountByUserId({
      id: user.id,
      differenceInHours: 1,
    });

    const usageValidation = validateUsage('external', {
      messagesInDay: messageCountDay,
      messagesInHour: messageCountHour,
    });

    if (!usageValidation.allowed) {
      return new ChatSDKError('rate_limit:chat').toResponse();
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message,
      });

      await saveChat({
        id,
        userId: user.id,
        title,
        visibility: selectedVisibilityType,
      });
    } else {
      if (chat.userId !== user.id) {
        return new ChatSDKError('forbidden:chat').toResponse();
      }
    }

    const previousMessages = await getMessagesByChatId({ id });

    const messages = appendClientMessage({
      // @ts-expect-error: todo add type conversion from DBMessage[] to UIMessage[]
      messages: previousMessages,
      message,
    });

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: 'user',
          parts: message.parts,
          attachments: message.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    const stream = createDataStream({
      execute: (dataStream) => {
        try {
          // Personalize system prompt with userName if provided
          const baseSystemPrompt = enhancedSystemPrompt({ selectedChatModel, requestHints });
          const personalizedSystemPrompt = userName
            ? `User's name is ${userName}. Address the user by name in responses. ${baseSystemPrompt}`
            : baseSystemPrompt;
          // Create contextual tools with tenant/token information
          const contextualCreateTicket = {
            ...createTicket,
            execute: async (params: any) => {
              return createTicket.execute(params, { 
                _context: { 
                  tenant, 
                  token,
                  userId,
                  source,
                  userName 
                } 
              } as any);
            }
          };

          const result = streamText({
            model: myProvider.languageModel(selectedChatModel),
            system: personalizedSystemPrompt,
            messages,
            // Remove maxSteps to disable step-based responses
            experimental_activeTools: ['getWeather', 'getPathRAGKnowledge', 'createTicket'],
            experimental_transform: smoothStream({ chunking: 'word' }),
            experimental_generateMessageId: generateUUID,
            tools: {
              getWeather,
              getPathRAGKnowledge,
              createTicket: contextualCreateTicket,
            },
            onFinish: async ({ response }) => {
              if (user?.id) {
                try {
                  const assistantId = getTrailingMessageId({
                    messages: response.messages.filter(
                      (message) => message.role === 'assistant',
                    ),
                  });

                  if (!assistantId) {
                    throw new Error('No assistant message found!');
                  }

                  const [, assistantMessage] = appendResponseMessages({
                    messages: [message],
                    responseMessages: response.messages,
                  });

                  await saveMessages({
                    messages: [
                      {
                        id: assistantId,
                        chatId: id,
                        role: assistantMessage.role,
                        parts: assistantMessage.parts,
                        attachments:
                          assistantMessage.experimental_attachments ?? [],
                        createdAt: new Date(),
                      },
                    ],
                  });
                } catch (_) {
                  console.error('Failed to save embed chat');
                }
              }
            },
            experimental_telemetry: {
              isEnabled: isProductionEnvironment,
              functionId: 'embed-stream-text',
            },
          });

          result.consumeStream();

          result.mergeIntoDataStream(dataStream, {
            sendReasoning: false,
          });
        } catch (error) {
          console.error('Error in embed streamText execution:', error);
          dataStream.writeData({
            type: 'error',
            error: error instanceof Error ? error.message : String(error)
          });
        }
      },
      onError: () => {
        return 'Oops, an error occurred in embed chat!';
      },
    });

    return new Response(stream);
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Unexpected error in POST /api/embed/chat:', error);
    return new Response(
      JSON.stringify({
        error: 'An unexpected error occurred in embed chat.',
        message: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}

export async function GET(request: Request) {
  return new Response(null, { status: 204 });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  const userId = searchParams.get('user_id');
  const source = searchParams.get('source');
  const token = searchParams.get('token');
  const tenant = searchParams.get('tenant');
  const userName = searchParams.get('user_name');

  if (!id || !userId || !source) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const userData = {
    externalId: userId,
    source: source as any,
    metadata: {
      token,
      tenant,
      userName,
      lastAccess: new Date().toISOString(),
    },
  };

  const user = await createOrGetExternalUser(userData);
  const chat = await getChatById({ id });

  if (chat.userId !== user.id) {
    return new ChatSDKError('forbidden:chat').toResponse();
  }

  const deletedChat = await deleteChatById({ id });

  return Response.json(deletedChat, { status: 200 });
}
