import { test, expect } from "@playwright/test"

test("Truy cập /embed với query hợp lệ và xác nhận trang hiển thị", async ({ page }) => {
  const url =
    "/embed?user_id=1&source=EMS&token=e5cbf3fa-6d96-4bcb-a8c3-26dff6cb19ee&tenant=edu3.dotb.cloud&user_name=Administrator"
  // Đổi thành domain production nếu cần, ví dụ: https://your-domain.com
  const baseUrl = process.env.PLAYWRIGHT_BASE_URL || "http://localhost:3000"
  await page.goto(baseUrl + url)
  // Kiểm tra tiêu đề hoặc text đặc trưng, sửa selector nếu cần cho phù hợp UI embed
  await expect(page).toHaveTitle(/embed/i)
  // Hoặc kiểm tra text đặc trưng
  await expect(page.locator("body")).toContainText(/embed|chat|administrator/i)
})

test("Truy cập /embed với user Nam Do", async ({ page }) => {
  const url =
    "/embed?user_id=1&source=EMS&token=e5cbf3fa-6d96-4bcb-a8c3-26dff6cb19ee&tenant=edu3.dotb.cloud&user_name=Nam Do"
  const baseUrl = process.env.PLAYWRIGHT_BASE_URL || "http://localhost:3000"

  await page.goto(baseUrl + url)

  // Kiểm tra trang load thành công
  await expect(page).toHaveTitle(/embed/i)

  // Kiểm tra body có chứa nội dung liên quan đến embed hoặc chat
  await expect(page.locator("body")).toContainText(/embed|chat/i)

  // Kiểm tra không có lỗi hiển thị
  await expect(page.locator("body")).not.toContainText(/error|404|not found/i)

  // Chờ một chút để trang load hoàn toàn
  await page.waitForTimeout(2000)

  // Kiểm tra response status
  const response = await page.goto(baseUrl + url)
  expect(response?.status()).toBe(200)
})