import { test, expect } from "@playwright/test"

test("<PERSON><PERSON><PERSON> cập /embed với query hợp lệ và xác nhận trang hiển thị", async ({ page }) => {
  const url =
    "/embed?user_id=1&source=EMS&token=e5cbf3fa-6d96-4bcb-a8c3-26dff6cb19ee&tenant=edu3.dotb.cloud&user_name=Administrator"
  // Đổi thành domain production nếu cần, ví dụ: https://your-domain.com
  const baseUrl = process.env.PLAYWRIGHT_BASE_URL || "http://localhost:3000"
  await page.goto(baseUrl + url)
  // Kiểm tra tiêu đề hoặc text đặc trưng, sửa selector nếu cần cho phù hợp UI embed
  await expect(page).toHaveTitle(/embed/i)
  // Hoặc kiểm tra text đặc trưng
  await expect(page.locator("body")).toContainText(/embed|chat|administrator/i)
})